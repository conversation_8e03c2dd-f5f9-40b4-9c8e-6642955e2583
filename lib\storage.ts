// 简单的本地存储工具类
export class LocalStorage {
  private static readonly CONTENT_KEY = 'markdown-editor-content';
  private static readonly SETTINGS_KEY = 'markdown-editor-settings';

  // 保存内容
  static saveContent(content: string): void {
    try {
      localStorage.setItem(this.CONTENT_KEY, content);
    } catch (error) {
      console.error('Failed to save content:', error);
    }
  }

  // 加载内容
  static loadContent(): string {
    try {
      return localStorage.getItem(this.CONTENT_KEY) || '';
    } catch (error) {
      console.error('Failed to load content:', error);
      return '';
    }
  }

  // 保存设置
  static saveSettings(settings: Record<string, any>): void {
    try {
      localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }

  // 加载设置
  static loadSettings(): Record<string, any> {
    try {
      const settings = localStorage.getItem(this.SETTINGS_KEY);
      return settings ? JSON.parse(settings) : {};
    } catch (error) {
      console.error('Failed to load settings:', error);
      return {};
    }
  }

  // 清除所有数据
  static clear(): void {
    try {
      localStorage.removeItem(this.CONTENT_KEY);
      localStorage.removeItem(this.SETTINGS_KEY);
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
  }
}

// 自动保存Hook
import { useEffect, useRef } from 'react';

export function useAutoSave(content: string, delay: number = 1000) {
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // 设置新的定时器
    timeoutRef.current = setTimeout(() => {
      LocalStorage.saveContent(content);
    }, delay);

    // 清理函数
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [content, delay]);

  // 组件卸载时立即保存
  useEffect(() => {
    return () => {
      LocalStorage.saveContent(content);
    };
  }, [content]);
}
