'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  GitBranch,
  GitCommit,
  GitPullRequest,
  Plus,
  RefreshCw,
  Upload,
  Download,
  Settings,
  Clock,
  FileText,
  Trash2,
  Minus,
  RotateCcw,
  ChevronDown,
  Check,
  X,
  GitFork
} from 'lucide-react';
import { SimpleGitService, CommitInfo, FileStatus, RepoInfo, GitConfig, BranchInfo } from '@/lib/simpleGit';
import { GitConfigDialog } from './GitConfigDialog';
import { RepoConnectDialog } from './RepoConnectDialog';
import { RepoSelector } from '@/components/github/RepoSelector';
import { useSession } from 'next-auth/react';

interface GitPanelProps {
  onFileSelect?: (filepath: string) => void;
  currentFile?: string | null;
  currentFileContent?: string; // 添加当前文件内容属性
  gitStatusVersion?: number; // 添加Git状态版本控制
}

export function GitPanel({ onFileSelect, currentFile, currentFileContent, gitStatusVersion }: GitPanelProps) {
  const { data: session } = useSession();
  const [gitService] = useState(() => SimpleGitService.getInstance());
  const [currentRepo, setCurrentRepo] = useState<RepoInfo | null>(null);
  const [currentBranch, setCurrentBranch] = useState<string>('main');
  const [fileStatus, setFileStatus] = useState<FileStatus[]>([]);
  const [commitHistory, setCommitHistory] = useState<CommitInfo[]>([]);
  const [commitMessage, setCommitMessage] = useState('');
  const [loading, setLoading] = useState(false);
  
  // 分支管理状态
  const [branches, setBranches] = useState<BranchInfo[]>([]);
  const [showBranchSelector, setShowBranchSelector] = useState(false);
  const [newBranchName, setNewBranchName] = useState('');
  const [showCreateBranch, setShowCreateBranch] = useState(false);

  // Dialog states
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [repoDialogOpen, setRepoDialogOpen] = useState(false);
  const [githubRepoSelectorOpen, setGithubRepoSelectorOpen] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [showNewFileInput, setShowNewFileInput] = useState(false);

  useEffect(() => {
    loadGitStatus();
  }, []);

  // 监听Git状态版本变化，自动刷新状态
  useEffect(() => {
    if (gitStatusVersion !== undefined) {
      console.log(`Git状态版本变化: ${gitStatusVersion}, 刷新状态`);
      loadGitStatus();
    }
  }, [gitStatusVersion]);

  // 当GitHub登录状态变化时，自动配置Git信息
  useEffect(() => {
    if (session?.user) {
      const githubConfig = {
        name: session.user.githubName || session.user.name || session.user.githubLogin || 'GitHub User',
        email: session.user.githubEmail || session.user.email || `${session.user.githubLogin}@users.noreply.github.com`,
        token: session.accessToken || ''
      };

      // 自动设置Git配置
      gitService.setConfig(githubConfig);
      console.log('自动配置Git信息:', githubConfig);
    }
  }, [session]);

  // 点击外部关闭分支选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showBranchSelector) {
        const target = event.target as Element;
        if (!target.closest('.branch-selector')) {
          setShowBranchSelector(false);
          setShowCreateBranch(false);
          setNewBranchName('');
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showBranchSelector]);

  const handleResetGitData = () => {
    if (confirm('确定要重置所有Git数据吗？这将清除所有本地文件状态和提交历史。')) {
      console.log('🧹 用户手动重置Git数据...');
      if (typeof window !== 'undefined') {
        localStorage.removeItem('simple-git-data');
        console.log('✅ localStorage已清理');
      }
      // 重新加载页面以应用重置
      window.location.reload();
    }
  };

  const loadGitStatus = async () => {
    try {
      const repo = gitService.getCurrentRepo();
      setCurrentRepo(repo);

      if (repo) {
        const branch = gitService.getCurrentBranch();
        const status = gitService.getFileStatus();
        const history = gitService.getCommitHistory();
        const branchList = gitService.getBranches();

        setCurrentBranch(branch);
        setFileStatus(status);
        setCommitHistory(history);
        setBranches(branchList);

        console.log('Git状态已更新');
      }
    } catch (error) {
      console.error('加载Git状态失败:', error);
    }
  };

  // 分支管理处理函数
  const handleSwitchBranch = async (branchName: string) => {
    const success = gitService.switchBranch(branchName);
    if (success) {
      setShowBranchSelector(false);
      await loadGitStatus();
      console.log(`✅ 已切换到分支: ${branchName}`);
    } else {
      alert('切换分支失败，请确保没有未提交的更改');
    }
  };

  const handleCreateBranch = () => {
    if (!newBranchName.trim()) {
      alert('请输入分支名称');
      return;
    }

    if (!/^[a-zA-Z0-9\-_\/]+$/.test(newBranchName)) {
      alert('分支名称只能包含字母、数字、横线、下划线和斜杠');
      return;
    }

    const success = gitService.createBranch(newBranchName);
    if (success) {
      setNewBranchName('');
      setShowCreateBranch(false);
      loadGitStatus();
      console.log(`✅ 已创建分支: ${newBranchName}`);
    } else {
      alert('创建分支失败，分支名称可能已存在');
    }
  };

  const handleDeleteBranch = (branchName: string) => {
    if (confirm(`确定要删除分支 "${branchName}" 吗？此操作无法撤销。`)) {
      const success = gitService.deleteBranch(branchName);
      if (success) {
        loadGitStatus();
        console.log(`✅ 已删除分支: ${branchName}`);
      } else {
        alert('删除分支失败，无法删除当前分支或主分支');
      }
    }
  };

  // 撤销文件修改
  const handleDiscardChanges = async (filepath: string) => {
    const confirmMessage = `确定要撤销对 "${filepath}" 的所有修改吗？\n\n此操作将：\n- 恢复文件到最后提交的版本\n- 丢失所有未保存的更改\n- 无法撤销\n\n请确认您要继续。`;
    
    if (confirm(confirmMessage)) {
      const success = gitService.discardChanges(filepath);
      if (success) {
        // 如果撤销的是当前正在编辑的文件，需要更新编辑器内容
        if (currentFile === filepath) {
          const restoredContent = gitService.getFileContent(filepath);
          if (restoredContent && onFileSelect) {
            console.log(`🔄 更新编辑器内容: ${filepath}`);
            onFileSelect(filepath); // 重新选择文件以更新编辑器
          }
        }
        await loadGitStatus();
        console.log(`✅ 已撤销文件修改: ${filepath}`);
      } else {
        alert(`撤销失败: 无法恢复文件 "${filepath}"`);
      }
    }
  };

  // 撤销所有未暂存的修改
  const handleDiscardAllChanges = async () => {
    const fileStatus = gitService.getFileStatus();
    const unstagedFiles = fileStatus.filter(f => f.status === 'modified' || f.status === 'new');
    
    if (unstagedFiles.length === 0) {
      alert('没有未暂存的修改需要撤销。');
      return;
    }
    
    const confirmMessage = `确定要撤销所有 ${unstagedFiles.length} 个未暂存的修改吗？\n\n此操作将：\n- 恢复所有文件到最后提交的版本\n- 删除所有新建的文件\n- 丢失所有未保存的更改\n- 无法撤销\n\n受影响的文件：\n${unstagedFiles.map(f => `• ${f.filepath}`).join('\n')}\n\n请确认您要继续。`;
    
    if (confirm(confirmMessage)) {
      const discardedCount = gitService.discardAllChanges();
      if (discardedCount > 0) {
        // 如果当前编辑的文件被撤销了，需要更新编辑器
        const wasCurrentFileAffected = unstagedFiles.some(f => f.filepath === currentFile);
        if (wasCurrentFileAffected && currentFile && onFileSelect) {
          console.log(`🔄 当前文件被撤销，更新编辑器内容: ${currentFile}`);
          onFileSelect(currentFile);
        }
        await loadGitStatus();
        alert(`✅ 已撤销 ${discardedCount} 个文件的修改`);
      } else {
        alert('撤销失败，请重试。');
      }
    }
  };

  const handleCommit = async () => {
    if (!commitMessage.trim()) {
      alert('请输入提交信息');
      return;
    }

    // 如果是GitHub登录用户，确保配置已设置
    if (session?.user && !gitService.getConfig()) {
      const githubConfig = {
        name: session.user.githubName || session.user.name || session.user.githubLogin || 'GitHub User',
        email: session.user.githubEmail || session.user.email || `${session.user.githubLogin}@users.noreply.github.com`,
        token: session.accessToken || ''
      };
      gitService.setConfig(githubConfig);
    }

    // 对于非GitHub用户，检查Git配置
    if (!session?.user && !gitService.getConfig()) {
      alert('请先设置Git配置');
      setConfigDialogOpen(true);
      return;
    }

    setLoading(true);
    try {
      // 提交已暂存的文件
      await gitService.commit(commitMessage);
      setCommitMessage('');

      // 刷新状态
      await loadGitStatus();
      alert('提交成功！');
    } catch (error) {
      console.error('Commit failed:', error);
      alert(`提交失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handlePush = async () => {
    if (!session?.accessToken || !currentRepo) {
      alert('需要GitHub登录和仓库连接才能推送');
      return;
    }

    // 确保保存当前编辑的文件内容
    if (currentFile && currentFileContent) {
      console.log(`保存当前编辑文件: ${currentFile}, 内容长度: ${currentFileContent.length}`);
      gitService.updateFile(currentFile, currentFileContent);
      // 立即刷新状态以检测更改
      await loadGitStatus();
    } else if (currentFile && typeof window !== 'undefined') {
      // 尝试从编辑器获取当前内容作为备用方案
      const editorElement = document.querySelector('.cm-editor .cm-content');
      if (editorElement) {
        const currentContent = editorElement.textContent || '';
        if (currentContent.trim()) {
          gitService.updateFile(currentFile, currentContent);
          console.log(`从编辑器保存当前文件: ${currentFile}, 内容长度: ${currentContent.length}`);
          // 立即刷新状态以检测更改
          await loadGitStatus();
        }
      }
    }

    // 检查是否有已提交的文件
    let committedFiles = Array.from(gitService.getCommittedFiles());
    console.log('已提交但未推送的文件:', committedFiles);

    // 如果没有已提交的文件，检查是否有更改需要处理
    if (committedFiles.length === 0) {
      const fileStatus = gitService.getFileStatus();
      const unstagedChanges = fileStatus.filter(f => f.status === 'modified' || f.status === 'new');
      const stagedChanges = fileStatus.filter(f => f.status === 'staged');

      if (unstagedChanges.length > 0) {
        // 有未暂存的更改，询问用户是否要自动处理
        const autoStageAndCommit = confirm(
          `检测到 ${unstagedChanges.length} 个未暂存的更改。\n\n` +
          `要推送到GitHub，需要先暂存和提交这些更改。\n\n` +
          `点击"确定"自动暂存并提交，或点击"取消"手动操作。`
        );

        if (autoStageAndCommit) {
          try {
            // 自动暂存所有更改
            gitService.stageAllChanges();
            
            // 自动提交
            const commitMessage = `自动提交: 更新 ${unstagedChanges.map(f => f.filepath).join(', ')}`;
            await gitService.commit(commitMessage);
            
            // 刷新状态
            await loadGitStatus();
            
            // 重新获取已提交的文件
            committedFiles = Array.from(gitService.getCommittedFiles());
            console.log('自动提交后的文件:', committedFiles);
          } catch (error) {
            console.error('自动提交失败:', error);
            alert(`自动提交失败: ${error}\n请手动暂存和提交您的更改。`);
            return;
          }
        } else {
          alert('请先暂存和提交您的更改，然后再推送。');
          return;
        }
      } else if (stagedChanges.length > 0) {
        // 有已暂存但未提交的更改
        alert('您有已暂存的更改需要先提交。请输入提交信息并点击"提交"按钮。');
        return;
      } else {
        // 没有任何更改
        alert('没有更改需要推送。');
        return;
      }
    }

    // 现在开始推送已提交的文件
    if (committedFiles.length === 0) {
      alert('没有已提交的文件需要推送。');
      return;
    }

    setLoading(true);
    try {
      const { GitHubService } = await import('@/lib/github');
      const githubService = new GitHubService(session.accessToken);

      console.log('开始推送已提交文件:', committedFiles);

      let pushedCount = 0;
      let errors: string[] = [];

      for (const filepath of committedFiles) {
        try {
          const content = gitService.getFileContent(filepath);
          console.log(`处理已提交文件 ${filepath}`);
          console.log(`- 内容长度: ${content?.length || 0}`);
          
          // 确保内容不为空
          if (!content || content.trim().length === 0) {
            console.warn(`跳过空文件: ${filepath}`);
            continue;
          }

          console.log(`- 内容预览(前100字符):`, content.substring(0, 100) + (content.length > 100 ? '...' : ''));

          // 尝试获取文件的SHA（如果文件已存在）
          let sha: string | undefined;
          try {
            console.log(`检查远程文件是否存在: ${filepath}`);
            const existingFile = await githubService.getRepoContents(
              currentRepo.owner,
              currentRepo.repo,
              filepath
            );
            if (Array.isArray(existingFile)) {
              // 如果返回数组，说明是目录，跳过
              console.log(`跳过目录: ${filepath}`);
              continue;
            }
            sha = existingFile.sha;
            console.log(`远程文件已存在，SHA: ${sha}`);
          } catch (error) {
            console.log(`远程文件不存在，将创建新文件: ${filepath}`);
            sha = undefined;
          }

          // 创建或更新文件
          console.log(`开始推送文件: ${filepath}`);
          console.log(`- SHA: ${sha || '无(新文件)'}`);
          console.log(`- 内容长度: ${content.length}`);

          try {
            const result = await githubService.createOrUpdateFile(
              currentRepo.owner,
              currentRepo.repo,
              filepath,
              content,
              `更新 ${filepath}`,
              sha
            );
            console.log(`✅ 文件推送成功: ${filepath}`, result);
            pushedCount++;
          } catch (pushError: any) {
            console.error(`❌ 推送文件 ${filepath} 失败:`, pushError);

            // 如果是SHA相关错误，尝试强制创建新文件
            if (pushError.message?.includes('sha') || pushError.message?.includes('422')) {
              console.log(`尝试强制创建新文件: ${filepath}`);
              try {
                const retryResult = await githubService.createOrUpdateFile(
                  currentRepo.owner,
                  currentRepo.repo,
                  filepath,
                  content,
                  `强制创建 ${filepath}`,
                  undefined // 不提供SHA，强制创建
                );
                console.log(`✅ 强制创建成功: ${filepath}`, retryResult);
                pushedCount++;
              } catch (retryError) {
                console.error(`❌ 强制创建也失败: ${filepath}`, retryError);
                throw retryError;
              }
            } else {
              throw pushError;
            }
          }
        } catch (error) {
          console.error(`处理文件 ${filepath} 时出错:`, error);
          errors.push(`${filepath}: ${error}`);
        }
      }

      // 标记推送成功的文件和提交
      if (pushedCount > 0) {
        gitService.markAsPushed(committedFiles);
        // 标记所有提交为已推送
        gitService.markCommitsAsPushed();
      }

      await loadGitStatus();

      if (errors.length > 0) {
        console.error('推送错误汇总:', errors);
        alert(`推送完成，但有 ${errors.length} 个文件失败。成功推送 ${pushedCount} 个文件。\n错误详情请查看控制台。`);
      } else {
        alert(`推送成功！更新了 ${pushedCount} 个文件`);
      }
    } catch (error) {
      console.error('Push failed:', error);
      alert(`推送失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handlePull = async () => {
    if (!session?.accessToken || !currentRepo) {
      alert('需要GitHub登录和仓库连接才能拉取');
      return;
    }

    setLoading(true);
    try {
      // 使用GitHub API拉取真实文件
      const { GitHubService } = await import('@/lib/github');
      const githubService = new GitHubService(session.accessToken);

      // 获取仓库文件列表
      const files = await githubService.getRepoContents(
        currentRepo.owner,
        currentRepo.repo
      );

      // 确保files是数组类型
      if (!Array.isArray(files)) {
        throw new Error('无法获取文件列表');
      }

      // 清空现有文件
      gitService.clearFiles();

      // 拉取所有Markdown文件并建立新的基线
      const pulledFiles = new Map<string, string>();
      for (const file of files) {
        if (file.type === 'file' && (file.name.endsWith('.md') || file.name.endsWith('.txt'))) {
          try {
            const content = await githubService.getFileContent(
              currentRepo.owner,
              currentRepo.repo,
              file.path
            );
            gitService.createFile(file.path, content);
            pulledFiles.set(file.path, content);
          } catch (error) {
            console.warn(`跳过文件 ${file.path}:`, error);
          }
        }
      }

      // 更新基线版本
      gitService.updateBaseline(pulledFiles);

      await loadGitStatus();
      alert(`拉取成功！获取了 ${pulledFiles.size} 个文件`);
    } catch (error) {
      console.error('Pull failed:', error);
      alert(`拉取失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateFile = () => {
    if (!newFileName.trim()) {
      alert('请输入文件名');
      return;
    }

    let filename = newFileName.trim();
    if (!filename.endsWith('.md')) {
      filename += '.md';
    }

    if (gitService.getFileList().includes(filename)) {
      alert('文件已存在');
      return;
    }

    gitService.createFile(filename, `# ${filename.replace('.md', '')}\n\n开始编写您的内容...`);
    setNewFileName('');
    setShowNewFileInput(false);
    loadGitStatus();

    // 自动选择新创建的文件
    if (onFileSelect) {
      onFileSelect(filename);
    }
  };

  const handleStageFile = (filepath: string) => {
    gitService.stageFile(filepath);
    loadGitStatus();
  };

  const handleUnstageFile = (filepath: string) => {
    gitService.unstageFile(filepath);
    loadGitStatus();
  };

  const handleStageAll = () => {
    gitService.stageAllChanges();
    loadGitStatus();
  };

  const getStatusIcon = (status: FileStatus['status']) => {
    switch (status) {
      case 'new':
        return <Plus className="w-3 h-3 text-green-500" />;
      case 'modified':
        return <GitCommit className="w-3 h-3 text-yellow-500" />;
      case 'staged':
        return <GitCommit className="w-3 h-3 text-blue-500" />;
      case 'deleted':
        return <span className="w-3 h-3 text-red-500">×</span>;
      default:
        return null;
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-3 border-b border-border">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium">源代码控制</h3>
          <div className="flex items-center space-x-1">
            {!session?.user && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setConfigDialogOpen(true)}
                title="设置Git配置"
              >
                <Settings className="w-3 h-3" />
              </Button>
            )}
            {session ? (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setGithubRepoSelectorOpen(true)}
                title="选择GitHub仓库"
              >
                <GitBranch className="w-3 h-3" />
              </Button>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setRepoDialogOpen(true)}
                title="连接本地仓库"
              >
                <GitBranch className="w-3 h-3" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={loadGitStatus}
              disabled={loading}
            >
              <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResetGitData}
              disabled={loading}
              title="重置Git数据（清理localStorage）"
            >
              <Trash2 className="w-3 h-3 text-red-500" />
            </Button>
          </div>
        </div>

        {currentRepo && (
          <div className="space-y-2">
            {/* 分支控制器 */}
            <div className="relative branch-selector">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowBranchSelector(!showBranchSelector)}
                className="w-full justify-between text-xs font-normal h-7 px-2"
                title="点击切换分支"
              >
                <div className="flex items-center space-x-1.5">
                  <GitBranch className="w-3 h-3" />
                  <span className="truncate">{currentBranch}</span>
                  {branches.length > 1 && (
                    <span className="text-xs bg-muted px-1.5 py-0.5 rounded text-muted-foreground">
                      {branches.length}
                    </span>
                  )}
                </div>
                <ChevronDown className={`w-3 h-3 transition-transform ${showBranchSelector ? 'rotate-180' : ''}`} />
              </Button>

              {/* 分支选择器下拉菜单 */}
              {showBranchSelector && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-background border border-border rounded-md shadow-lg z-10">
                  <div className="p-1 max-h-48 overflow-y-auto">
                    {/* 当前分支列表 */}
                    <div className="space-y-1">
                      {branches.map((branch) => (
                        <div key={branch.name}>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSwitchBranch(branch.name)}
                            disabled={branch.isActive}
                            className={`w-full justify-between text-xs font-normal h-8 px-2 ${
                              branch.isActive ? 'bg-muted' : ''
                            }`}
                          >
                            <div className="flex items-center space-x-1.5 min-w-0 flex-1">
                              <GitBranch className="w-3 h-3 flex-shrink-0" />
                              <div className="min-w-0 flex-1">
                                <div className="flex items-center space-x-1">
                                  <span className="truncate font-medium">{branch.name}</span>
                                  {branch.isActive && <Check className="w-3 h-3 text-green-600" />}
                                </div>
                                {branch.lastCommit && (
                                  <div className="text-xs text-muted-foreground truncate">
                                    {branch.lastCommit.message}
                                  </div>
                                )}
                              </div>
                            </div>
                            {!branch.isActive && branch.name !== 'main' && branch.name !== 'master' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteBranch(branch.name);
                                }}
                                className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-100 hover:text-red-700"
                                title="删除分支"
                              >
                                <X className="w-3 h-3" />
                              </Button>
                            )}
                          </Button>
                        </div>
                      ))}
                    </div>

                    <Separator className="my-1" />

                    {/* 创建新分支 */}
                    {!showCreateBranch ? (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowCreateBranch(true)}
                        className="w-full justify-start text-xs font-normal h-6 px-2"
                      >
                        <Plus className="w-3 h-3 mr-1.5" />
                        创建新分支
                      </Button>
                    ) : (
                      <div className="space-y-1 p-1">
                        <Input
                          placeholder="分支名称"
                          value={newBranchName}
                          onChange={(e) => setNewBranchName(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleCreateBranch();
                            } else if (e.key === 'Escape') {
                              setShowCreateBranch(false);
                              setNewBranchName('');
                            }
                          }}
                          className="text-xs h-6"
                          autoFocus
                        />
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleCreateBranch}
                            className="flex-1 h-6 text-xs"
                          >
                            <Check className="w-3 h-3 mr-1" />
                            创建
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setShowCreateBranch(false);
                              setNewBranchName('');
                            }}
                            className="h-6 w-6 p-0"
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* 仓库信息 */}
            <div className="text-xs text-muted-foreground">
              {currentRepo.owner}/{currentRepo.repo}
            </div>
          </div>
        )}
      </div>

      <ScrollArea className="flex-1">
        {currentRepo ? (
          <div className="p-3 space-y-4">
            {/* Files Section */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-xs font-medium">文件 ({gitService.getFileList().length})</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowNewFileInput(!showNewFileInput)}
                  className="h-6 w-6 p-0"
                >
                  <Plus className="w-3 h-3" />
                </Button>
              </div>

              {showNewFileInput && (
                <div className="mb-2 space-y-1">
                  <Input
                    placeholder="文件名 (自动添加.md)"
                    value={newFileName}
                    onChange={(e) => setNewFileName(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleCreateFile();
                      } else if (e.key === 'Escape') {
                        setShowNewFileInput(false);
                        setNewFileName('');
                      }
                    }}
                    className="text-xs"
                    autoFocus
                  />
                  <div className="flex space-x-1">
                    <Button
                      size="sm"
                      onClick={handleCreateFile}
                      className="h-6 text-xs"
                    >
                      创建
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setShowNewFileInput(false);
                        setNewFileName('');
                      }}
                      className="h-6 text-xs"
                    >
                      取消
                    </Button>
                  </div>
                </div>
              )}

              {gitService.getFileList().length > 0 ? (
                <div className="space-y-1">
                  {gitService.getFileList().map((filepath) => (
                    <div
                      key={filepath}
                      className={`flex items-center space-x-2 text-xs p-1 rounded hover:bg-muted/50 cursor-pointer ${
                        currentFile === filepath ? 'bg-muted' : ''
                      }`}
                      onClick={() => onFileSelect?.(filepath)}
                    >
                      <FileText className="w-3 h-3 text-muted-foreground" />
                      <span className="flex-1 truncate">{filepath}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-xs text-muted-foreground">没有文件</div>
              )}
            </div>

            <Separator />

            {/* Changes Section - 统一风格 */}
            <div className="space-y-3">
              {/* 未暂存的更改 */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <div className="text-xs text-muted-foreground uppercase tracking-wide">
                    更改 ({fileStatus.filter(f => f.status !== 'staged').length})
                  </div>
                  {fileStatus.filter(f => f.status === 'modified' || f.status === 'new').length > 0 && (
                    <div className="flex items-center space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleDiscardAllChanges}
                        className="h-6 w-6 p-0"
                        title="撤销所有未暂存的修改"
                      >
                        <RotateCcw className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleStageAll}
                        className="h-6 w-6 p-0"
                        title="暂存所有修改"
                      >
                        <Plus className="w-3 h-3" />
                      </Button>
                    </div>
                  )}
                </div>
                
                {fileStatus.filter(f => f.status !== 'staged').length > 0 ? (
                  <div className="space-y-1">
                    {fileStatus.filter(f => f.status !== 'staged').map((file) => (
                      <div
                        key={file.filepath}
                        className={`group flex items-center justify-between hover:bg-muted/50 rounded transition-colors ${
                          currentFile === file.filepath ? 'bg-muted' : ''
                        }`}
                      >
                        {/* 文件信息区域 */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onFileSelect?.(file.filepath)}
                          className="flex-1 justify-start text-xs font-normal h-6 px-2"
                        >
                          <div className="flex items-center space-x-2 min-w-0 flex-1">
                            <div className="flex items-center space-x-1.5 flex-shrink-0">
                              {getStatusIcon(file.status)}
                              <span className="text-xs font-mono text-muted-foreground w-3">
                                {file.status === 'modified' ? 'M' : file.status === 'new' ? 'A' : 'D'}
                              </span>
                            </div>
                            <span className="truncate" title={file.filepath}>
                              {file.filepath}
                            </span>
                          </div>
                        </Button>
                        
                        {/* 操作按钮区域 */}
                        {(file.status === 'modified' || file.status === 'new') && (
                          <div className="flex items-center space-x-0.5 opacity-0 group-hover:opacity-100 transition-opacity pr-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDiscardChanges(file.filepath);
                              }}
                              className="h-5 w-5 p-0"
                              title="撤销此文件的所有修改"
                            >
                              <RotateCcw className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStageFile(file.filepath);
                              }}
                              className="h-5 w-5 p-0"
                              title="将此文件添加到暂存区"
                            >
                              <Plus className="w-3 h-3" />
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-xs text-muted-foreground p-4 text-center">
                    没有更改
                  </div>
                )}
              </div>

              {/* 已暂存的更改 */}
              {fileStatus.filter(f => f.status === 'staged').length > 0 && (
                <>
                  <Separator />
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-xs text-muted-foreground uppercase tracking-wide">
                        已暂存的更改 ({fileStatus.filter(f => f.status === 'staged').length})
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      {fileStatus.filter(f => f.status === 'staged').map((file) => (
                        <div
                          key={file.filepath}
                          className={`group flex items-center justify-between hover:bg-muted/50 rounded transition-colors ${
                            currentFile === file.filepath ? 'bg-muted' : ''
                          }`}
                        >
                          {/* 文件信息区域 */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onFileSelect?.(file.filepath)}
                            className="flex-1 justify-start text-xs font-normal h-6 px-2"
                          >
                            <div className="flex items-center space-x-2 min-w-0 flex-1">
                              <div className="flex items-center space-x-1.5 flex-shrink-0">
                                <GitCommit className="w-3 h-3" />
                                <span className="text-xs font-mono text-muted-foreground w-3">
                                  S
                                </span>
                              </div>
                              <span className="truncate" title={file.filepath}>
                                {file.filepath}
                              </span>
                            </div>
                          </Button>
                          
                          {/* 操作按钮区域 */}
                          <div className="flex items-center opacity-0 group-hover:opacity-100 transition-opacity pr-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleUnstageFile(file.filepath);
                              }}
                              className="h-5 w-5 p-0"
                              title="将此文件移出暂存区（保留修改）"
                            >
                              <Minus className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>

            <Separator />

            {/* Commit Section - 统一风格 */}
            {fileStatus.filter(f => f.status === 'staged').length > 0 && (
              <div className="space-y-3">
                <div className="text-xs text-muted-foreground uppercase tracking-wide">
                  提交更改
                </div>
                
                <div className="space-y-2">
                  <Input
                    placeholder="提交信息..."
                    value={commitMessage}
                    onChange={(e) => setCommitMessage(e.target.value)}
                    className="text-xs"
                  />
                  
                  <Button
                    onClick={handleCommit}
                    disabled={loading || !commitMessage.trim() || (!session?.user && !gitService.getConfig())}
                    className="w-full h-8 text-xs"
                    variant={commitMessage.trim() ? "default" : "secondary"}
                  >
                    <GitCommit className="w-3 h-3 mr-2" />
                    {!session?.user && !gitService.getConfig() 
                      ? '请先设置Git配置' 
                      : `提交 ${fileStatus.filter(f => f.status === 'staged').length} 个文件`
                    }
                  </Button>
                </div>
              </div>
            )}

            <Separator />

            {/* Sync Section - 统一风格 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-xs text-muted-foreground uppercase tracking-wide">
                  同步
                </div>
                {commitHistory.filter(c => !c.pushed).length > 0 && (
                  <div className="text-xs text-muted-foreground">
                    {commitHistory.filter(c => !c.pushed).length} 待推送
                  </div>
                )}
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePush}
                  disabled={loading}
                  className="flex-1 h-8 text-xs"
                >
                  <Upload className="w-3 h-3 mr-2" />
                  推送
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePull}
                  disabled={loading}
                  className="flex-1 h-8 text-xs"
                >
                  <Download className="w-3 h-3 mr-2" />
                  拉取
                </Button>
              </div>
            </div>

            <Separator />

            {/* Commit History - 统一风格 */}
            <div>
              <div className="text-xs text-muted-foreground uppercase tracking-wide mb-2">
                最近提交 ({commitHistory.length})
              </div>
              
              {commitHistory.length > 0 ? (
                <div className="space-y-1 max-h-48 overflow-y-auto">
                  {commitHistory.map((commit) => (
                    <div
                      key={commit.id}
                      className="p-2 text-xs border rounded hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center justify-between mb-1">
                        <div className="font-medium truncate flex-1 text-xs">
                          {commit.message}
                        </div>
                        <div className="text-xs text-muted-foreground ml-2">
                          {commit.pushed ? '已推送' : '待推送'}
                        </div>
                      </div>
                      <div className="text-muted-foreground flex items-center justify-between">
                        <div>{commit.author}</div>
                        <div>{commit.date}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-xs text-muted-foreground p-4 text-center">
                  暂无提交历史
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="p-3 text-center space-y-4">
            {!session?.user && !gitService.getConfig() ? (
              <>
                <div className="text-sm text-muted-foreground mb-4">
                  请先设置Git配置
                </div>
                <Button
                  size="sm"
                  onClick={() => setConfigDialogOpen(true)}
                >
                  <Settings className="w-3 h-3 mr-1" />
                  设置Git配置
                </Button>
              </>
            ) : (
              <>
                <div className="text-sm text-muted-foreground mb-4">
                  {session?.user ? '选择GitHub仓库开始编辑' : '还没有连接Git仓库'}
                </div>
                {session ? (
                  <Button
                    size="sm"
                    onClick={() => setGithubRepoSelectorOpen(true)}
                  >
                    <GitBranch className="w-3 h-3 mr-1" />
                    选择GitHub仓库
                  </Button>
                ) : (
                  <Button
                    size="sm"
                    onClick={() => setRepoDialogOpen(true)}
                  >
                    <GitBranch className="w-3 h-3 mr-1" />
                    连接本地仓库
                  </Button>
                )}
              </>
            )}
          </div>
        )}
      </ScrollArea>

      {/* Dialogs */}
      <GitConfigDialog
        open={configDialogOpen}
        onOpenChange={setConfigDialogOpen}
        onConfigSaved={() => loadGitStatus()}
      />
      
      <RepoConnectDialog
        open={repoDialogOpen}
        onOpenChange={setRepoDialogOpen}
        onRepoConnected={() => loadGitStatus()}
      />

      <RepoSelector
        open={githubRepoSelectorOpen}
        onOpenChange={setGithubRepoSelectorOpen}
        onRepoSelected={async (repo) => {
          // 🔧 强制清理localStorage中的旧状态
          console.log('🧹 清理localStorage中的Git数据...');
          if (typeof window !== 'undefined') {
            localStorage.removeItem('simple-git-data');
          }
          
          // 连接到选中的GitHub仓库 - 跳过示例文件
          const repoInfo = {
            owner: repo.owner.login,
            repo: repo.name,
            branch: repo.default_branch
          };
          
          console.log('🔗 连接GitHub仓库，跳过示例文件');
          gitService.connectRepo(repoInfo, true); // 🔧 跳过示例文件

          // 自动拉取仓库文件
          if (session?.accessToken) {
            setLoading(true);
            try {
              const { GitHubService } = await import('@/lib/github');
              const githubService = new GitHubService(session.accessToken);

              // 获取仓库文件列表
              const files = await githubService.getRepoContents(repo.owner.login, repo.name);

              // 确保files是数组类型
              if (!Array.isArray(files)) {
                throw new Error('无法获取文件列表');
              }

              // 清空现有文件（如果有的话）
              gitService.clearFiles();

              // 拉取所有Markdown和文本文件并建立基线
              const pulledFiles = new Map<string, string>();
              for (const file of files) {
                if (file.type === 'file' && (file.name.endsWith('.md') || file.name.endsWith('.txt'))) {
                  try {
                    const content = await githubService.getFileContent(
                      repo.owner.login,
                      repo.name,
                      file.path
                    );
                    gitService.createFile(file.path, content);
                    pulledFiles.set(file.path, content);
                  } catch (error) {
                    console.warn(`跳过文件 ${file.path}:`, error);
                  }
                }
              }

              // 更新基线版本
              gitService.updateBaseline(pulledFiles);

              console.log(`✅ 自动拉取了 ${pulledFiles.size} 个文件`);
              
              // �� 强制刷新编辑器内容，避免显示示例内容
              const files_list = gitService.getFileList();
              if (files_list.length > 0) {
                const defaultFile = files_list.find(f => f === 'README.md') || files_list[0];
                console.log(`🔄 自动选择文件: ${defaultFile}`);
                if (onFileSelect) {
                  onFileSelect(defaultFile);
                }
              }
              
            } catch (error) {
              console.error('❌ 自动拉取失败:', error);
            } finally {
              setLoading(false);
            }
          }

          loadGitStatus();
        }}
      />
    </div>
  );
}
