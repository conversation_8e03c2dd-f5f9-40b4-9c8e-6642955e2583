'use client';

import React, { useState, useEffect, useRef } from 'react';
import { TopBar } from '@/components/layout/TopBar';
import { LeftSidebar } from '@/components/layout/LeftSidebar';
import { RightSidebar } from '@/components/layout/RightSidebar';
import { StatusBar } from '@/components/layout/StatusBar';
import { EditorArea } from '@/components/editor/EditorArea';
import { CommitDetailView } from '@/components/git/CommitDetailView';
import { ThemeProvider, useTheme } from '@/components/providers/ThemeProvider';
import { SessionProvider } from '@/components/providers/SessionProvider';

function AppContent() {
  const { theme, toggleTheme } = useTheme();
  const [isLeftSidebarCollapsed, setIsLeftSidebarCollapsed] = useState(false);
  const [isRightSidebarOpen, setIsRightSidebarOpen] = useState(true);
  const [isSaved, setIsSaved] = useState(true);
  const [content, setContent] = useState('');
  const [currentBranch, setCurrentBranch] = useState('main');
  const [currentFile, setCurrentFile] = useState<string | null>(null);
  const [gitStatusVersion, setGitStatusVersion] = useState(0); // 添加版本控制来触发Git面板刷新
  const [commitContent, setCommitContent] = useState<string>('');
  const [commitFilename, setCommitFilename] = useState<string>('');
  const [isViewingCommit, setIsViewingCommit] = useState(false);
  
  // 使用useRef确保获取到最新的currentFile值
  const currentFileRef = useRef<string | null>(null);

  // 强制刷新Git状态
  const refreshGitStatus = () => {
    setGitStatusVersion(prev => prev + 1);
  };

  // 同步currentFile到ref
  useEffect(() => {
    currentFileRef.current = currentFile;
    console.log(`📌 更新currentFileRef: ${currentFile}`);
  }, [currentFile]);

  // 初始化时加载默认文件
  useEffect(() => {
    const loadDefaultFile = async () => {
      try {
        const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();
        const files = gitService.getFileList();
        if (files.length > 0 && !currentFile) {
          const defaultFile = files.find(f => f === 'README.md') || files[0];
          console.log(`初始化加载默认文件: ${defaultFile}`);
          setCurrentFile(defaultFile);
          const fileContent = gitService.getFileContent(defaultFile);
          setContent(fileContent || '');
        }
      } catch (error) {
        console.error('加载默认文件失败:', error);
      }
    };

    loadDefaultFile();
  }, []); // 只在组件挂载时执行一次

  // 处理提交内容变化
  const handleCommitContentChange = (content: string, filename: string) => {
    setCommitContent(content);
    setCommitFilename(filename);
    setIsViewingCommit(true);
  };

  // 处理文件选择
  const handleFileSelect = async (filepath: string) => {
    try {
      console.log(`=== 文件选择事件 ===`);
      console.log(`- 选择的文件: ${filepath}`);
      console.log(`- 当前文件: ${currentFile}`);

      // 退出提交查看模式
      setIsViewingCommit(false);

      // 先保存当前编辑的内容（如果有的话）
      if (currentFileRef.current && content && currentFileRef.current !== filepath) {
        console.log(`💾 选择新文件前，先保存当前文件: ${currentFileRef.current}`);
        const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();
        gitService.updateFile(currentFileRef.current, content);
        console.log(`- 保存的内容长度: ${content.length}`);
      }

      setCurrentFile(filepath);

      // 从Git服务获取文件内容
      const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();
      const fileContent = gitService.getFileContent(filepath);

      console.log(`- 获取到的内容长度: ${fileContent?.length || 0}`);
      console.log(`- 内容预览: "${fileContent?.substring(0, 50) || ''}..."`);

      setContent(fileContent || '');
      setIsSaved(true);

      console.log(`- 文件选择完成: ${filepath}`);
    } catch (error) {
      console.error('加载文件失败:', error);
      setContent(`# ${filepath}\n\n文件加载失败，请重试。`);
    }
  };

  // 处理内容保存
  const handleContentSave = async () => {
    const actualCurrentFile = currentFileRef.current;
    if (!actualCurrentFile) return;

    try {
      const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();
      gitService.updateFile(actualCurrentFile, content);
      setIsSaved(true);
      console.log(`文件已保存到Git服务: ${actualCurrentFile}, 内容长度: ${content.length}`);
      refreshGitStatus(); // 保存成功后刷新Git状态
    } catch (error) {
      console.error('保存文件失败:', error);
    }
  };

  return (
    <div className="h-screen flex flex-col bg-background text-foreground">
      {/* Top Bar */}
      <TopBar
        isDarkMode={theme === 'dark'}
        onThemeToggle={toggleTheme}
        isSaved={isSaved}
        content={content}
        onSave={handleContentSave}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden main-content-area">
        {/* Left Sidebar */}
        <LeftSidebar
          isCollapsed={isLeftSidebarCollapsed}
          onToggle={() => setIsLeftSidebarCollapsed(!isLeftSidebarCollapsed)}
          onFileSelect={handleFileSelect}
          currentFile={currentFile}
          currentFileContent={content}
          gitStatusVersion={gitStatusVersion}
          onCommitContentChange={handleCommitContentChange}
        />

        {/* Editor Area or Commit Detail View */}
        <div className="flex-1 min-w-0 flex flex-col editor-content-wrapper">
          {isViewingCommit ? (
            <CommitDetailView
              commitData={commitContent}
              filename={commitFilename}
              onFileSelect={handleFileSelect}
            />
          ) : (
            <EditorArea
              onSettingsToggle={() => setIsRightSidebarOpen(!isRightSidebarOpen)}
              content={content}
              currentFile={currentFile}
            onContentChange={async (newContent, editorCurrentFile) => {
              console.log(`=== 编辑器内容变化 ===`);
              console.log(`- 状态中的currentFile: ${currentFile}`);
              console.log(`- Ref中的currentFile: ${currentFileRef.current}`);
              console.log(`- 编辑器传递的currentFile: ${editorCurrentFile}`);
              console.log(`- 新内容长度: ${newContent.length}`);
              console.log(`- 新内容预览: "${newContent.substring(0, 50)}..."`);
              console.log(`- 之前内容长度: ${content.length}`);

              setContent(newContent);
              setIsSaved(false);

              // 使用编辑器传递的文件名，这是最可靠的
              const targetFile = editorCurrentFile || currentFileRef.current;

              console.log(`- 最终使用的目标文件: ${targetFile}`);

              // 自动同步到Git服务
              if (targetFile) {
                try {
                  const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();

                  // 检查同步前的状态
                  const beforeContent = gitService.getFileContent(targetFile);
                  console.log(`- 同步前Git中的内容长度: ${beforeContent?.length || 0}`);

                  console.log(`- 同步内容到文件: ${targetFile}`);
                  gitService.updateFile(targetFile, newContent);

                  // 检查同步后的状态
                  const afterContent = gitService.getFileContent(targetFile);
                  console.log(`- 同步后Git中的内容长度: ${afterContent?.length || 0}`);

                  console.log(`✅ 内容已同步到Git服务: ${targetFile}, 长度: ${newContent.length}`);

                  // 🔧 确保立即刷新Git状态，无需手动保存
                  console.log(`🔄 自动刷新Git状态...`);
                  refreshGitStatus();
                } catch (error) {
                  console.error('❌ 同步到Git服务失败:', error);
                }
              } else {
                console.warn('⚠️ 无法确定目标文件，无法同步内容');
              }
            }}
            />
          )}
        </div>

        {/* Right Sidebar */}
        <RightSidebar
          isOpen={isRightSidebarOpen}
          onClose={() => setIsRightSidebarOpen(false)}
        />
      </div>

      {/* Status Bar */}
      <StatusBar
        wordCount={content.split(/\s+/).filter(word => word.length > 0).length}
        currentBranch={currentBranch}
        lastSaved="2 分钟前"
      />
    </div>
  );
}

export default function Home() {
  return (
    <SessionProvider>
      <ThemeProvider>
        <AppContent />
      </ThemeProvider>
    </SessionProvider>
  );
}