'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  List,
  FileText,
  GitBranch,
  Search,
  ChevronRight,
  ChevronDown,
  File,
  Folder,
  FolderOpen,
  History,
} from 'lucide-react';
import { GitPanel } from '@/components/git/GitPanel';
import { HistoryPanel } from '@/components/git/HistoryPanel';

interface LeftSidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  onFileSelect?: (filepath: string) => void;
  currentFile?: string | null;
  currentFileContent?: string; // 添加当前文件内容属性
  gitStatusVersion?: number; // 添加Git状态版本控制
  onCommitContentChange?: (content: string, filename: string) => void;
}

export function LeftSidebar({ isCollapsed, onToggle, onFileSelect, currentFile, currentFileContent, gitStatusVersion, onCommitContentChange }: LeftSidebarProps) {
  const [activeTab, setActiveTab] = useState('outline');

  const sidebarTabs = [
    { id: 'outline', icon: List, label: '大纲' },
    { id: 'files', icon: FileText, label: '文件' },
    { id: 'git', icon: GitBranch, label: 'Git' },
    { id: 'history', icon: History, label: '历史' },
    { id: 'search', icon: Search, label: '搜索' },
  ];

  return (
    <div className="flex h-full bg-secondary/30">
      {/* Icon Bar */}
      <div className="w-12 bg-secondary border-r border-border flex flex-col items-center py-2 space-y-1">
        {sidebarTabs.map((tab) => (
          <Button
            key={tab.id}
            variant={activeTab === tab.id ? 'default' : 'ghost'}
            size="sm"
            className="w-8 h-8 p-0"
            onClick={() => setActiveTab(tab.id)}
          >
            <tab.icon className="w-4 h-4" />
          </Button>
        ))}
      </div>

      {/* Content Panel */}
      {!isCollapsed && (
        <div className="w-64 bg-background border-r border-border flex flex-col">
          {/* Header */}
          <div className="h-8 flex items-center justify-between px-3 bg-secondary/50">
            <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              {sidebarTabs.find((tab) => tab.id === activeTab)?.label}
            </span>
          </div>

          <Separator />

          <ScrollArea className="flex-1">
            <div className="p-2">
              {activeTab === 'outline' && <OutlinePanel />}
              {activeTab === 'files' && <FilesPanel />}
              {activeTab === 'git' && (
                <GitPanel
                  onFileSelect={onFileSelect}
                  currentFile={currentFile}
                  currentFileContent={currentFileContent}
                  gitStatusVersion={gitStatusVersion}
                />
              )}
              {activeTab === 'history' && (
                <HistoryPanel
                  onFileSelect={onFileSelect}
                  currentFile={currentFile}
                  onCommitContentChange={onCommitContentChange}
                />
              )}
              {activeTab === 'search' && <SearchPanel />}
            </div>
          </ScrollArea>
        </div>
      )}
    </div>
  );
}

function OutlinePanel() {
  return (
    <div className="space-y-1">
      <div className="text-xs text-muted-foreground mb-2">文档大纲</div>
      {[
        '介绍',
        '快速开始',
        '核心功能',
        '配置',
        '高级用法',
        'API 参考',
      ].map((item, index) => (
        <Button
          key={index}
          variant="ghost"
          size="sm"
          className="w-full justify-start text-xs font-normal h-6 px-2"
        >
          <span className="mr-2 text-muted-foreground">
            {index < 2 ? 'H1' : index < 4 ? 'H2' : 'H3'}
          </span>
          {item}
        </Button>
      ))}
    </div>
  );
}

function FilesPanel() {
  const [expandedFolders, setExpandedFolders] = useState<string[]>(['docs']);

  const toggleFolder = (folder: string) => {
    setExpandedFolders((prev) =>
      prev.includes(folder)
        ? prev.filter((f) => f !== folder)
        : [...prev, folder]
    );
  };

  return (
    <div className="space-y-1">
      <div className="text-xs text-muted-foreground mb-2">项目文件</div>
      
      <Button
        variant="ghost"
        size="sm"
        className="w-full justify-start text-xs font-normal h-6 px-2"
        onClick={() => toggleFolder('docs')}
      >
        {expandedFolders.includes('docs') ? (
          <FolderOpen className="w-3 h-3 mr-2" />
        ) : (
          <Folder className="w-3 h-3 mr-2" />
        )}
        docs
        {expandedFolders.includes('docs') ? (
          <ChevronDown className="w-3 h-3 ml-auto" />
        ) : (
          <ChevronRight className="w-3 h-3 ml-auto" />
        )}
      </Button>

      {expandedFolders.includes('docs') && (
        <div className="ml-4 space-y-1">
          {['README.md', 'guide.md', 'api.md'].map((file) => (
            <Button
              key={file}
              variant="ghost"
              size="sm"
              className="w-full justify-start text-xs font-normal h-6 px-2"
            >
              <File className="w-3 h-3 mr-2" />
              {file}
            </Button>
          ))}
        </div>
      )}
    </div>
  );
}



function SearchPanel() {
  return (
    <div className="space-y-3">
      <div className="text-xs text-muted-foreground mb-2">搜索</div>
      <input
        type="text"
        placeholder="在文件中搜索..."
        className="w-full text-xs px-2 py-1 bg-background border border-border rounded"
      />
      <div className="text-xs text-muted-foreground">
        未找到结果
      </div>
    </div>
  );
}